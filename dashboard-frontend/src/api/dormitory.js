import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    console.log('API请求:', config.url, config.params)
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    console.log('API响应:', response.config.url, response.data)
    return response.data
  },
  error => {
    console.error('响应错误:', error)
    // 网络错误或服务器错误时返回模拟数据
    if (error.code === 'ECONNABORTED' || error.response?.status >= 500) {
      console.warn('使用模拟数据')
      return getMockData(error.config.url)
    }
    return Promise.reject(error)
  }
)

// 模拟数据
function getMockData(url) {
  if (url.includes('/statistics') || url.includes('/realtime')) {
    return {
      success: true,
      message: '查询成功',
      data: {
        statistics: {
          totalPersons: 1200,
          inDormitoryPersons: 980,
          outDormitoryPersons: 220,
          personsWithoutRecords: 0,
          returnRate: 81.67,
          dataSource: 'mock_data'
        },
        records: generateMockRecords(30, true),
        recordCount: 30,
        date: new Date().toISOString().split('T')[0],
        dataSource: 'mock_data'
      }
    }
  }
  
  if (url.includes('/records')) {
    return {
      success: true,
      message: '查询成功',
      count: 30,
      data: generateMockRecords(30, true),
      dataSource: 'mock_data'
    }
  }
  
  return { success: false, message: '未知接口' }
}

// 生成模拟记录
function generateMockRecords(count, isReturned) {
  const records = []
  const names = ['张三', '李四', '王五', '赵六', '钱七', '孙八', '周九', '吴十']
  
  for (let i = 0; i < count; i++) {
    const now = new Date()
    const randomMinutes = Math.floor(Math.random() * 120) // 2小时内的随机时间
    const passTime = new Date(now.getTime() - randomMinutes * 60000)
    
    records.push({
      personCode: `2024${String(Math.floor(Math.random() * 9999)).padStart(4, '0')}`,
      personName: names[Math.floor(Math.random() * names.length)],
      lastPassTimeStr: passTime.toLocaleTimeString('zh-CN'),
      lastInOrOut: isReturned ? 1 : 2,
      lastInOrOutDesc: isReturned ? '进入寝室' : '离开寝室',
      isInDormitory: isReturned,
      dormitoryStatusDesc: isReturned ? '已归寝室' : '未归寝室',
      lastDeviceName: `门禁设备${Math.floor(Math.random() * 10) + 1}`,
      lastAreaName: `${Math.floor(Math.random() * 5) + 1}号楼`,
      dormitoryName: `${Math.floor(Math.random() * 5) + 1}号楼${Math.floor(Math.random() * 6) + 1}${String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')}`,
      buildingName: `${Math.floor(Math.random() * 5) + 1}号楼`,
      floor: `${Math.floor(Math.random() * 6) + 1}`,
      roomNumber: String(Math.floor(Math.random() * 20) + 1).padStart(2, '0')
    })
  }
  
  return records.sort((a, b) => new Date(b.lastPassTimeStr) - new Date(a.lastPassTimeStr))
}

// API接口定义
export const dormitoryAPI = {
  // 获取大屏实时数据（主要接口）- 一次请求获取统计和记录
  getRealtimeData(limit = 30) {
    return api.get('/dashboard/realtime', { params: { limit } })
  },

  // 获取大屏统计信息（基于Redis缓存）
  getStatistics() {
    return api.get('/dashboard/statistics')
  },

  // 获取多楼栋统计信息（支持楼栋筛选）
  getStatisticsByBuildings(buildingCodes = []) {
    const params = buildingCodes.length > 0 ? { buildingCodes: buildingCodes.join(',') } : {}
    return api.get('/dashboard/statistics/buildings', { params })
  },

  // 获取大屏进出记录（基于Redis缓存）
  getRecords(limit = 30) {
    return api.get('/dashboard/records', { params: { limit } })
  },

  // 统一查询接口（优化版本）- 支持楼栋筛选和数据刷新
  queryDashboardData(buildingCodes = null, forceRefresh = false) {
    const requestBody = {}

    if (buildingCodes && buildingCodes.length > 0) {
      requestBody.buildingCodes = buildingCodes
    }

    if (forceRefresh) {
      requestBody.refresh = true
    }

    return api.post('/dashboard/query', requestBody)
  },

  // 强制刷新缓存数据（保留兼容性）
  refreshData() {
    return api.post('/dashboard/refresh')
  },

  // 大屏健康检查
  healthCheck() {
    return api.get('/dashboard/health')
  },

  // 获取缓存状态
  getCacheStatus() {
    return api.get('/dashboard/cache/status')
  },

  // 清除缓存
  clearCache() {
    return api.post('/dashboard/cache/clear')
  },

  // 分页查询所有学生状态（其他页面使用）
  getAllStudentsStatus(page = 0, size = 20, personName = null, dormitoryStatus = null) {
    const params = { page, size }
    if (personName && personName.trim()) params.personName = personName.trim()
    if (dormitoryStatus !== null && dormitoryStatus !== undefined) params.dormitoryStatus = dormitoryStatus
    return api.get('/dormitory-status/students', { params })
  },

  // 寝室管理相关API
  
  /**
   * 获取所有楼栋列表
   */
  getBuildings() {
    return api.get('/dormitory-management/buildings')
  },

  /**
   * 获取指定楼栋的楼层列表
   */
  getBuildingFloors(buildingCode) {
    return api.get(`/dormitory-management/buildings/${buildingCode}/floors`)
  },

  /**
   * 获取指定楼栋楼层的寝室列表（含人员信息）
   */
  getFloorDormitories(buildingCode, floor) {
    return api.get(`/dormitory-management/buildings/${buildingCode}/floors/${floor}/dormitories`)
  },

  /**
   * 获取寝室管理统计信息
   */
  getManagementStatistics() {
    return api.get('/dormitory-management/statistics')
  }
}

export default dormitoryAPI
