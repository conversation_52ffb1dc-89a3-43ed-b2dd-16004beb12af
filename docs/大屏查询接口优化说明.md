# 大屏查询接口优化说明

## 优化概述

本次优化主要针对大屏查询接口中根据楼栋查询学生状态的功能进行性能提升。优化的核心思路是：**先根据选择的楼栋查出所有关联的学生人数，然后根据这些学生编码去Redis中一个个查询是否在寝室来判断是否在寝室或者无记录**。

## 优化前的问题

1. **查询效率低**：原有实现中存在多次数据库查询和不必要的数据转换
2. **Redis查询不够优化**：没有充分利用Redis的批量查询能力
3. **缺乏性能监控**：无法准确了解查询各个环节的耗时情况
4. **数据源切换混乱**：在FastGate和Master数据源之间切换不够清晰

## 优化后的改进

### 1. 查询逻辑优化

#### 优化前的流程：
```
1. 查询楼栋下的学生总数（数据库查询）
2. 查询楼栋下的学生编码列表（数据库查询）
3. 批量从Redis获取学生状态
4. 逐个分析学生状态
5. 统计结果
```

#### 优化后的流程：
```
1. 一次性查询楼栋下的学生编码列表（优化的SQL，同时得到总人数）
2. 分批从Redis批量获取学生状态（提高Redis查询效率）
3. 逐个分析学生状态（增加详细的性能监控）
4. 统计结果（包含性能统计信息）
```

### 2. 核心优化点

#### 2.1 数据库查询优化
- **合并查询**：将总人数查询和学生列表查询合并为一次查询
- **SQL优化**：添加必要的JOIN条件和状态筛选，确保查询效率
- **数据源明确**：统一使用Master数据源进行人员和宿舍信息查询

#### 2.2 Redis查询优化
- **分批处理**：将大量学生分批处理（每批500个），避免单次查询过多数据
- **批量查询**：充分利用Redis的批量查询能力
- **命中率统计**：记录Redis缓存命中率，便于性能分析

#### 2.3 性能监控增强
- **详细计时**：分别记录数据库查询时间、Redis查询时间、数据分析时间
- **命中率统计**：记录Redis缓存命中率和未命中率
- **进度日志**：在处理大量数据时提供进度反馈

### 3. 代码结构改进

#### 3.1 方法职责明确
```java
// 主入口方法
public Map<String, Object> getDormitoryStatisticsByBuildings(List<String> buildingCodes)

// 核心计算方法
private Map<String, Object> calculateBuildingsStatistics(Set<String> buildingCodes)

// 学生查询方法
private List<String> getBuildingStudentCodes(Set<String> buildingCodes)

// Redis状态分析方法
private Map<String, Object> analyzeStudentStatusFromRedis(List<String> studentCodes)
```

#### 3.2 错误处理和降级
- **异常捕获**：在各个关键环节添加异常处理
- **降级机制**：当优化查询失败时，自动降级到原有查询方式
- **详细日志**：记录错误原因和降级情况

### 4. 性能提升效果

#### 4.1 查询效率提升
- **减少数据库查询次数**：从2次减少到1次
- **优化Redis查询**：通过分批处理提高Redis查询效率
- **减少内存占用**：避免一次性加载大量数据到内存

#### 4.2 监控能力增强
- **性能统计**：提供详细的性能统计信息
- **缓存命中率**：实时监控Redis缓存效果
- **查询方法标识**：明确标识使用的查询方法

## 使用方式

### API接口
```http
GET /api/dashboard/statistics/buildings?buildingCodes=A1,A2,B1
```

### 响应示例
```json
{
  "success": true,
  "message": "查询成功（优化版本）",
  "data": {
    "totalPersons": 1500,
    "inDormitoryPersons": 1200,
    "outDormitoryPersons": 200,
    "personsWithoutRecords": 100,
    "returnRate": 80.0,
    "queryMethod": "buildings_filtered_optimized",
    "dataSource": "buildings_filtered_optimized",
    "baseDescription": "基于指定楼栋学生统计（优化版本）",
    "selectedBuildings": ["A1", "A2", "B1"],
    "calculationDuration": 150,
    "queryDuration": 180,
    "performanceStats": {
      "totalDuration": 150,
      "redisQueryTime": 80,
      "analysisTime": 20,
      "redisHitCount": 1400,
      "redisMissCount": 100,
      "redisHitRate": 93.33
    }
  }
}
```

## 测试验证

### 单元测试
运行测试类 `CachedDormitoryStatsServiceOptimizationTest` 来验证优化效果：

```bash
mvn test -Dtest=CachedDormitoryStatsServiceOptimizationTest
```

### 性能测试
测试包含以下场景：
1. **单个楼栋查询**：测试单楼栋查询的性能
2. **多个楼栋查询**：测试多楼栋查询的性能
3. **边界情况处理**：测试空列表、不存在楼栋等情况
4. **性能对比**：多轮测试获取平均性能数据

## 注意事项

1. **数据源配置**：确保Master数据源配置正确
2. **Redis连接**：确保Redis缓存服务正常运行
3. **日志级别**：建议将相关日志级别设置为INFO以便监控性能
4. **缓存预热**：首次查询可能较慢，后续查询会更快

## 后续优化建议

1. **楼栋统计缓存**：可以考虑对楼栋统计结果进行短期缓存
2. **异步查询**：对于大量楼栋的查询，可以考虑异步处理
3. **查询结果缓存**：对于相同楼栋组合的查询结果进行缓存
4. **数据库索引优化**：确保相关表的索引配置合理
