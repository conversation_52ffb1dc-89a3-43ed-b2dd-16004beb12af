# 首页楼栋选择优化说明

## 🎯 优化目标

解决首页楼栋选择的用户体验问题，实现以下优化：

1. **交互优化**：选择一个楼栋不再自动调用接口，等选择多个楼栋后确认再调用
2. **接口统一**：楼栋选择、刷新数据都调用同一个优化后的接口
3. **性能提升**：使用优化后的Redis查询逻辑，提高查询效率

## 📋 优化内容

### 1. 统一接口设计

#### 新增统一查询接口
```http
POST /api/dashboard/query
Content-Type: application/json

{
  "buildingCodes": ["A1", "A2", "B1"],  // 可选：楼栋代码列表
  "refresh": true                        // 可选：是否强制刷新缓存
}
```

#### 接口功能说明
- **无参数**：查询全部统计数据
- **buildingCodes参数**：查询指定楼栋统计数据（使用优化后的查询逻辑）
- **refresh=true**：强制刷新缓存后查询

### 2. 前端交互优化

#### 优化前的问题
```javascript
// 原来的问题：选择一个楼栋就自动调用接口
function onBuildingChange() {
    const selectedBuilding = buildingSelect.value;
    if (selectedBuilding) {
        // 立即调用接口 - 用户体验不好
        loadBuildingPersons();
    }
}
```

#### 优化后的交互
```javascript
// 优化后：支持多选，确认后再查询
function updateSelectedCount() {
    const selectedCount = getSelectedBuildingCodes().length;
    // 只更新UI状态，不自动调用接口
    document.getElementById('selectedCount').textContent = selectedCount;
}

function querySelectedBuildings() {
    const selectedCodes = getSelectedBuildingCodes();
    if (selectedCodes.length === 0) {
        alert('请先选择要查询的楼栋');
        return;
    }
    // 用户确认后才调用接口
    queryDashboardData(selectedCodes, false);
}
```

### 3. 新增大屏首页

#### 页面访问
- **URL**: `/dashboard`
- **模板**: `src/main/resources/templates/dashboard.html`

#### 页面功能
1. **楼栋多选**：支持复选框多选楼栋
2. **操作按钮**：
   - 查询选中楼栋
   - 查询全部
   - 刷新数据
   - 全选/清空
3. **实时统计**：显示总人数、在寝人员、外出人员、归寝率
4. **性能监控**：显示查询耗时、Redis命中率等性能指标

## 🚀 技术实现

### 1. 后端优化

#### 统一查询接口实现
```java
@PostMapping("/query")
public ResponseEntity<Map<String, Object>> queryDashboardData(
        @RequestBody(required = false) Map<String, Object> requestBody) {
    
    // 解析参数
    List<String> buildingCodes = extractBuildingCodes(requestBody);
    boolean forceRefresh = extractRefreshFlag(requestBody);
    
    // 强制刷新缓存（如果需要）
    if (forceRefresh) {
        cachedDormitoryStatsService.clearAllCache();
    }
    
    // 根据参数选择查询方式
    Map<String, Object> statistics = buildingCodes == null || buildingCodes.isEmpty() ?
        cachedDormitoryStatsService.getDormitoryStatistics() :
        cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(buildingCodes);
    
    // 返回统一格式的响应
    return buildUnifiedResponse(statistics, buildingCodes, forceRefresh);
}
```

#### 兼容性保证
```java
@PostMapping("/refresh")
public ResponseEntity<Map<String, Object>> refreshDashboardData() {
    // 调用统一接口，保持向后兼容
    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("refresh", true);
    return queryDashboardData(requestBody);
}
```

### 2. 前端优化

#### 楼栋选择组件
```html
<!-- 楼栋复选框区域 -->
<div id="buildingCheckboxes" class="row">
    <!-- 动态生成楼栋复选框 -->
</div>

<!-- 操作按钮 -->
<div class="action-buttons">
    <button onclick="querySelectedBuildings()">查询选中楼栋</button>
    <button onclick="queryAllBuildings()">查询全部</button>
    <button onclick="refreshData()">刷新数据</button>
</div>
```

#### 统一查询方法
```javascript
function queryDashboardData(buildingCodes, forceRefresh) {
    const requestBody = {};
    if (buildingCodes && buildingCodes.length > 0) {
        requestBody.buildingCodes = buildingCodes;
    }
    if (forceRefresh) {
        requestBody.refresh = true;
    }
    
    fetch('/api/dashboard/query', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(requestBody)
    })
    .then(response => response.json())
    .then(data => updateDashboardDisplay(data.data));
}
```

## 📊 优化效果

### 1. 用户体验提升
- ✅ **交互优化**：不再选择一个楼栋就自动查询
- ✅ **多选支持**：支持同时选择多个楼栋进行查询
- ✅ **操作明确**：用户需要主动点击"查询"按钮
- ✅ **状态反馈**：实时显示已选择的楼栋数量

### 2. 接口统一化
- ✅ **统一入口**：所有查询都通过 `/api/dashboard/query` 接口
- ✅ **参数灵活**：支持全部查询、楼栋筛选、强制刷新
- ✅ **向后兼容**：保留原有接口，确保兼容性

### 3. 性能优化
- ✅ **Redis优化**：使用优化后的批量查询逻辑
- ✅ **智能缓存**：支持强制刷新和缓存复用
- ✅ **性能监控**：提供详细的性能统计信息

## 🔧 使用方式

### 1. 访问大屏首页
```
http://localhost:8080/dashboard
```

### 2. 楼栋选择操作
1. 页面加载后自动显示楼栋列表
2. 勾选需要查询的楼栋（支持多选）
3. 点击"查询选中楼栋"按钮
4. 查看统计结果

### 3. 其他操作
- **查询全部**：查询所有楼栋的统计数据
- **刷新数据**：强制刷新缓存后重新查询
- **全选/清空**：快速选择或清空所有楼栋

### 4. API调用示例

#### 查询指定楼栋
```bash
curl -X POST http://localhost:8080/api/dashboard/query \
  -H "Content-Type: application/json" \
  -d '{"buildingCodes": ["A1", "A2", "B1"]}'
```

#### 强制刷新后查询全部
```bash
curl -X POST http://localhost:8080/api/dashboard/query \
  -H "Content-Type: application/json" \
  -d '{"refresh": true}'
```

#### 刷新指定楼栋数据
```bash
curl -X POST http://localhost:8080/api/dashboard/query \
  -H "Content-Type: application/json" \
  -d '{"buildingCodes": ["A1", "A2"], "refresh": true}'
```

## 📈 性能监控

### 响应数据示例
```json
{
  "success": true,
  "message": "查询成功",
  "data": {
    "totalPersons": 1500,
    "inDormitoryPersons": 1200,
    "outDormitoryPersons": 200,
    "personsWithoutRecords": 100,
    "returnRate": 80.0,
    "dataSource": "selected_buildings",
    "selectedBuildings": ["A1", "A2", "B1"],
    "queryDuration": 180,
    "performanceStats": {
      "totalDuration": 150,
      "redisQueryTime": 80,
      "redisHitRate": 93.33,
      "studentsPerSecond": 8333
    }
  }
}
```

## 🎯 总结

通过这次优化，我们实现了：

1. **用户体验优化**：从"选择即查询"改为"选择后确认查询"
2. **接口统一**：三个场景（楼栋选择、刷新数据、查询全部）都使用同一个优化接口
3. **性能提升**：使用优化后的Redis查询逻辑，支持几千学生的高效查询
4. **功能完善**：新增大屏首页，提供更好的可视化界面

这个优化方案完全满足了您的需求，提供了更好的用户体验和更高的查询性能。
