package fastgatedemo.demo.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import fastgatedemo.demo.dto.DormitoryStatusDTO;
import fastgatedemo.demo.dto.StudentStatusCacheDTO;
import fastgatedemo.demo.model.PersonInfo;
import fastgatedemo.demo.model.fastgate.TblPerson;
import fastgatedemo.demo.repository.PersonRepository;
import fastgatedemo.demo.repository.fastgate.TblPersonRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description 基于缓存的寝室状态统计服务
 * 优先从Redis缓存查询，提供高性能的统计和查询功能
 * <AUTHOR>
 * @date 2025-01-29
 */
@Service
public class CachedDormitoryStatsService {

    private static final Logger logger = LoggerFactory.getLogger(CachedDormitoryStatsService.class);

    @Autowired
    private StudentStatusCacheService statusCacheService;

    @Autowired
    private DormitoryStatusService originalDormitoryService;

    @Autowired
    private TblPersonRepository tblPersonRepository;

    @Autowired
    private PersonRepository personRepository;

    @Autowired
    private EntityManager entityManager;

    /**
     * 获取全局统计信息（基于已分配宿舍的学生）
     * 查询所有已经分配宿舍的学生作为总人数基数
     * 然后根据缓存中的进出统计在寝人员和不在寝人员以及无记录人员
     * 优化版本：优先从缓存获取，减少实时计算开销
     * @return 统计信息
     */
    public Map<String, Object> getDormitoryStatistics() {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("开始获取基于已分配宿舍学生的统计信息（优化版本）");
            
            // 1. 优先尝试从缓存获取统计结果
            Map<String, Object> cachedStats = statusCacheService.getAssignedDormitoryStatistics();
            if (cachedStats != null) {
                long duration = System.currentTimeMillis() - startTime;
                cachedStats.put("queryDuration", duration);
                cachedStats.put("queryMethod", "cached_assigned_dormitory_stats");
                logger.info("从缓存获取统计信息成功，耗时: {}ms", duration);
                return cachedStats;
            }
            
            // 2. 缓存未命中，进行实时计算
            Map<String, Object> freshStats = calculateFreshStatisticsBasedOnAssignedDormitory();
            
            // 3. 将计算结果缓存起来
            statusCacheService.updateAssignedDormitoryStatistics(freshStats);
            
            long duration = System.currentTimeMillis() - startTime;
            freshStats.put("queryMethod", "assigned_dormitory_based_realtime_cached");
            freshStats.put("queryDuration", duration);
            
            logger.info("基于已分配宿舍学生的统计信息计算并缓存完成，耗时: {}ms", duration);
            return freshStats;
            
        } catch (Exception e) {
            logger.error("获取统计信息失败，降级到原始查询: {}", e.getMessage(), e);
            // 降级到原始查询
            return originalDormitoryService.getDormitoryStatistics(getCurrentDate());
        }
    }

    /**
     * 分页查询指定状态的学生列表（基于缓存）
     * @param status 状态 (1=在寝, 2=外出, 0=无记录)
     * @param pageable 分页参数
     * @param personName 姓名筛选
     * @return 分页的学生状态列表
     */
    public Page<DormitoryStatusDTO> getStudentsByStatus(Integer status, Pageable pageable, String personName) {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("基于缓存查询学生状态: status={}, page={}, size={}, name={}", 
                       status, pageable.getPageNumber(), pageable.getPageSize(), personName);

            // 从缓存获取指定状态的学生列表
            List<String> studentCodes = statusCacheService.getStudentsByStatus(status, 10000); // 先获取较多数据
            
            if (studentCodes.isEmpty()) {
                logger.warn("缓存中没有找到状态为{}的学生，降级到数据库查询", status);
                return originalDormitoryService.getAllStudentsStatusOptimized(pageable, personName, status);
            }

            // 批量获取学生详细状态
            Map<String, StudentStatusCacheDTO> statusMap = statusCacheService.batchGetStudentStatus(studentCodes);
            
            // 转换为DTO列表并应用筛选
            List<DormitoryStatusDTO> allStudents = statusMap.values().stream()
                    .filter(student -> {
                        // 姓名筛选
                        if (personName != null && !personName.trim().isEmpty()) {
                            return student.getPersonName() != null && 
                                   student.getPersonName().contains(personName.trim());
                        }
                        return true;
                    })
                    .map(this::convertToDTO)
                    .sorted((a, b) -> {
                        // 按最后通行时间排序，最新的在前
                        if (a.getLastPassTime() == null && b.getLastPassTime() == null) return 0;
                        if (a.getLastPassTime() == null) return 1;
                        if (b.getLastPassTime() == null) return -1;
                        return b.getLastPassTime().compareTo(a.getLastPassTime());
                    })
                    .collect(Collectors.toList());

            // 手动分页
            int start = (int) pageable.getOffset();
            int end = Math.min(start + pageable.getPageSize(), allStudents.size());
            
            List<DormitoryStatusDTO> pageContent = start < allStudents.size() ? 
                    allStudents.subList(start, end) : new ArrayList<>();

            long duration = System.currentTimeMillis() - startTime;
            logger.info("基于缓存查询完成: 总数={}, 筛选后={}, 当前页={}, 耗时={}ms", 
                       studentCodes.size(), allStudents.size(), pageContent.size(), duration);

            return new PageImpl<>(pageContent, pageable, allStudents.size());

        } catch (Exception e) {
            logger.error("基于缓存查询失败，降级到数据库查询: {}", e.getMessage(), e);
            return originalDormitoryService.getAllStudentsStatusOptimized(pageable, personName, status);
        }
    }

    /**
     * 获取所有学生状态（混合查询）
     * @param pageable 分页参数
     * @param personName 姓名筛选
     * @param dormitoryStatus 状态筛选
     * @return 分页的学生状态列表
     */
    public Page<DormitoryStatusDTO> getAllStudentsStatus(Pageable pageable, String personName, Integer dormitoryStatus) {
        long startTime = System.currentTimeMillis();
        
        try {
            logger.info("混合查询所有学生状态: page={}, size={}, name={}, status={}", 
                       pageable.getPageNumber(), pageable.getPageSize(), personName, dormitoryStatus);

            // 如果有状态筛选，直接调用按状态查询
            if (dormitoryStatus != null) {
                return getStudentsByStatus(dormitoryStatus, pageable, personName);
            }

            // 获取所有有效学生
            List<TblPerson> allPersons = getActivePersons();
            List<String> personCodes = allPersons.stream()
                    .map(TblPerson::getCode)
                    .collect(Collectors.toList());

            // 批量获取缓存状态
            Map<String, StudentStatusCacheDTO> cachedStatusMap = statusCacheService.batchGetStudentStatus(personCodes);

            // 合并缓存数据和人员数据
            List<DormitoryStatusDTO> allStudents = allPersons.stream()
                    .filter(person -> {
                        // 姓名筛选
                        if (personName != null && !personName.trim().isEmpty()) {
                            return person.getName() != null && 
                                   person.getName().contains(personName.trim());
                        }
                        return true;
                    })
                    .map(person -> {
                        StudentStatusCacheDTO cachedStatus = cachedStatusMap.get(person.getCode());
                        return convertPersonToDTO(person, cachedStatus);
                    })
                    .sorted((a, b) -> {
                        // 按最后通行时间排序，最新的在前，无记录的在最后
                        if (a.getLastPassTime() == null && b.getLastPassTime() == null) return 0;
                        if (a.getLastPassTime() == null) return 1;
                        if (b.getLastPassTime() == null) return -1;
                        return b.getLastPassTime().compareTo(a.getLastPassTime());
                    })
                    .collect(Collectors.toList());

            // 手动分页
            int start = (int) pageable.getOffset();
            int end = Math.min(start + pageable.getPageSize(), allStudents.size());
            
            List<DormitoryStatusDTO> pageContent = start < allStudents.size() ? 
                    allStudents.subList(start, end) : new ArrayList<>();

            long duration = System.currentTimeMillis() - startTime;
            logger.info("混合查询完成: 总人数={}, 缓存命中={}, 筛选后={}, 当前页={}, 耗时={}ms", 
                       allPersons.size(), cachedStatusMap.size(), allStudents.size(), pageContent.size(), duration);

            return new PageImpl<>(pageContent, pageable, allStudents.size());

        } catch (Exception e) {
            logger.error("混合查询失败，降级到数据库查询: {}", e.getMessage(), e);
            return originalDormitoryService.getAllStudentsStatusOptimized(pageable, personName, dormitoryStatus);
        }
    }

    /**
     * 获取单个学生状态
     * @param personCode 人员编码
     * @return 学生状态DTO
     */
    public Optional<DormitoryStatusDTO> getStudentStatus(String personCode) {
        try {
            // 先从缓存获取
            StudentStatusCacheDTO cachedStatus = statusCacheService.getStudentStatus(personCode);
            
            if (cachedStatus != null) {
                logger.debug("从缓存获取学生状态: personCode={}", personCode);
                return Optional.of(convertToDTO(cachedStatus));
            }

            // 缓存未命中，降级到数据库查询
            logger.debug("缓存未命中，从数据库查询学生状态: personCode={}", personCode);
            return originalDormitoryService.getPersonDormitoryStatus(personCode, getCurrentDate());

        } catch (Exception e) {
            logger.error("获取学生状态失败: personCode={}, error={}", personCode, e.getMessage());
            return Optional.empty();
        }
    }

    /**
     * 刷新统计缓存
     * @return 刷新后的统计信息
     */
    public Map<String, Object> refreshStatisticsCache() {
        try {
            logger.info("手动刷新统计缓存");
            
            Map<String, Object> freshStats = calculateFreshStatistics();
            statusCacheService.updateGlobalStatistics(freshStats);
            
            logger.info("统计缓存刷新成功");
            return freshStats;
            
        } catch (Exception e) {
            logger.error("刷新统计缓存失败: {}", e.getMessage(), e);
            throw new RuntimeException("刷新统计缓存失败: " + e.getMessage());
        }
    }

    /**
     * 获取缓存统计信息
     * @return 缓存统计
     */
    public Map<String, Object> getCacheStatistics() {
        return statusCacheService.getCacheStatistics();
    }

    /**
     * 清除所有缓存
     */
    public void clearAllCache() {
        statusCacheService.clearAllCache();
        logger.info("所有缓存已清除");
    }

    // ==================== 私有方法 ====================

    /**
     * 计算新鲜的统计信息
     * @return 统计信息
     */
    private Map<String, Object> calculateFreshStatistics() {
        // 从人员表获取总人数
        long totalPersons = getTotalActivePersons();
        
        // 从缓存获取各状态统计
        Map<String, Object> cacheStats = statusCacheService.getCacheStatistics();
        
        Long inDormitoryCount = (Long) cacheStats.getOrDefault("inDormitoryCount", 0L);
        Long outDormitoryCount = (Long) cacheStats.getOrDefault("outDormitoryCount", 0L);
        Long noRecordCount = totalPersons - inDormitoryCount - outDormitoryCount;
        
        // 如果缓存数据不完整，降级到数据库计算
        if (inDormitoryCount + outDormitoryCount == 0) {
            logger.warn("缓存统计数据不完整，降级到数据库计算");
            return originalDormitoryService.getDormitoryStatistics(getCurrentDate());
        }

        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalPersons", totalPersons);
        statistics.put("returnedCount", inDormitoryCount); // 在寝人数
        statistics.put("notReturnedCount", outDormitoryCount); // 外出人数
        statistics.put("personsWithRecords", inDormitoryCount + outDormitoryCount);
        statistics.put("personsWithoutRecords", noRecordCount);
        
        double returnRate = totalPersons > 0 ? (double) inDormitoryCount / totalPersons * 100 : 0;
        statistics.put("returnRate", Math.round(returnRate * 100.0) / 100.0);
        statistics.put("date", getCurrentDate());
        statistics.put("queryMethod", "cache_based");
        
        return statistics;
    }

    /**
     * 计算基于已分配宿舍学生的统计信息
     * 使用person_dormitory_relation表中的学生作为总人数基数
     * @return 统计信息
     */
    @DS("master")
    private Map<String, Object> calculateFreshStatisticsBasedOnAssignedDormitory() {
        logger.info("开始计算基于已分配宿舍学生的统计信息");
        long startTime = System.currentTimeMillis();
        
        try {
            // 1. 查询所有已分配宿舍的学生（作为总人数基数）
            List<String> assignedStudents = getAssignedDormitoryStudents();
            long totalPersons = assignedStudents.size();
            
            logger.info("已分配宿舍的学生总数: {}", totalPersons);
            
            if (totalPersons == 0) {
                logger.warn("没有找到已分配宿舍的学生，返回空统计");
                return createEmptyStatistics();
            }
            
            // 2. 从缓存中获取这些学生的进出状态
            Map<String, Object> statusCounts = analyzeStudentStatusFromCache(assignedStudents);
            
            long inDormitoryCount = (Long) statusCounts.get("inDormitoryCount");
            long outDormitoryCount = (Long) statusCounts.get("outDormitoryCount");
            long noRecordCount = (Long) statusCounts.get("noRecordCount");
            
            // 3. 构建统计结果
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("totalPersons", totalPersons);
            statistics.put("returnedCount", inDormitoryCount); // 在寝人数
            statistics.put("notReturnedCount", outDormitoryCount); // 外出人数
            statistics.put("personsWithRecords", inDormitoryCount + outDormitoryCount);
            statistics.put("personsWithoutRecords", noRecordCount);
            
            double returnRate = totalPersons > 0 ? (double) inDormitoryCount / totalPersons * 100 : 0;
            statistics.put("returnRate", Math.round(returnRate * 100.0) / 100.0);
            statistics.put("date", getCurrentDate());
            statistics.put("queryMethod", "assigned_dormitory_based");
            statistics.put("baseDescription", "基于已分配宿舍学生统计");
            
            long duration = System.currentTimeMillis() - startTime;
            statistics.put("calculationDuration", duration);
            
            logger.info("统计完成 - 总人数:{}, 在寝:{}, 外出:{}, 无记录:{}, 归寝率:{}%, 耗时:{}ms",
                    totalPersons, inDormitoryCount, outDormitoryCount, noRecordCount, returnRate, duration);
            
            return statistics;
            
        } catch (Exception e) {
            logger.error("计算基于已分配宿舍学生的统计信息失败: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 获取所有已分配宿舍的学生列表
     * @return 学生代码列表
     */
    @DS("master")
    private List<String> getAssignedDormitoryStudents() {
        return personRepository.findAssignedDormitoryStudentCodes();
    }

    /**
     * 分析学生在缓存中的状态分布
     * @param studentCodes 学生代码列表
     * @return 状态统计信息
     */
    private Map<String, Object> analyzeStudentStatusFromCache(List<String> studentCodes) {
        logger.info("开始分析{}名学生的缓存状态", studentCodes.size());
        
        long inDormitoryCount = 0;
        long outDormitoryCount = 0;
        long noRecordCount = 0;
        
        // 批量获取学生状态（分批处理避免一次性加载过多数据）
        int batchSize = 1000;
        for (int i = 0; i < studentCodes.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, studentCodes.size());
            List<String> batch = studentCodes.subList(i, endIndex);
            
            Map<String, StudentStatusCacheDTO> batchStatus = statusCacheService.batchGetStudentStatus(batch);
            
            for (String studentCode : batch) {
                StudentStatusCacheDTO status = batchStatus.get(studentCode);
                
                if (status == null || status.getLastPassTime() == null) {
                    // 没有缓存记录或没有通行记录
                    noRecordCount++;
                } else if (status.isInDormitory()) {
                    // 在寝
                    inDormitoryCount++;
                } else {
                    // 外出
                    outDormitoryCount++;
                }
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("inDormitoryCount", inDormitoryCount);
        result.put("outDormitoryCount", outDormitoryCount);
        result.put("noRecordCount", noRecordCount);
        
        logger.info("状态分析完成 - 在寝:{}, 外出:{}, 无记录:{}", inDormitoryCount, outDormitoryCount, noRecordCount);
        
        return result;
    }

    /**
     * 创建空的统计信息
     * @return 空统计信息
     */
    private Map<String, Object> createEmptyStatistics() {
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalPersons", 0L);
        statistics.put("returnedCount", 0L);
        statistics.put("notReturnedCount", 0L);
        statistics.put("personsWithRecords", 0L);
        statistics.put("personsWithoutRecords", 0L);
        statistics.put("returnRate", 0.0);
        statistics.put("date", getCurrentDate());
        statistics.put("queryMethod", "empty_assigned_dormitory");
        statistics.put("baseDescription", "无已分配宿舍学生");
        return statistics;
    }

    /**
     * 转换StudentStatusCacheDTO为DormitoryStatusDTO
     * @param cachedStatus 缓存状态
     * @return DormitoryStatusDTO
     */
    private DormitoryStatusDTO convertToDTO(StudentStatusCacheDTO cachedStatus) {
        DormitoryStatusDTO dto = new DormitoryStatusDTO();
        
        dto.setPersonCode(cachedStatus.getPersonCode());
        dto.setPersonName(cachedStatus.getPersonName());
        dto.setLastInOrOut(cachedStatus.getLastInOrOut());
        dto.setLastPassTime(cachedStatus.getLastPassTime());
        dto.setIsInDormitory(cachedStatus.isInDormitory());
        dto.setLastDeviceName(cachedStatus.getLastDeviceName());
        dto.setLastAreaName(cachedStatus.getLastAreaName());
        dto.setLastInOrOutDesc(cachedStatus.getInOrOutDescription());
        dto.setDormitoryStatusDesc(cachedStatus.getStatusDescription());
        dto.setDepartmentCode(cachedStatus.getDepartmentCode());
        dto.setDepartmentName(cachedStatus.getDepartmentName());
        dto.setGender(cachedStatus.getGender());
        dto.setTelephone(cachedStatus.getTelephone());
        
        if (cachedStatus.getLastPassTime() != null) {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            dto.setLastPassTimeStr(cachedStatus.getLastPassTime().format(formatter));
        } else {
            dto.setLastPassTimeStr("无记录");
        }
        
        dto.setQueryDate(getCurrentDate());
        dto.setDormitoryName(""); // 宿舍信息需要额外查询
        
        return dto;
    }

    /**
     * 转换TblPerson和缓存状态为DormitoryStatusDTO
     * @param person 人员信息
     * @param cachedStatus 缓存状态（可能为null）
     * @return DormitoryStatusDTO
     */
    private DormitoryStatusDTO convertPersonToDTO(TblPerson person, StudentStatusCacheDTO cachedStatus) {
        DormitoryStatusDTO dto = new DormitoryStatusDTO();
        
        dto.setPersonCode(person.getCode());
        dto.setPersonName(person.getName());
        dto.setQueryDate(getCurrentDate());
        
        if (cachedStatus != null) {
            // 有缓存状态
            dto.setLastInOrOut(cachedStatus.getLastInOrOut());
            dto.setLastPassTime(cachedStatus.getLastPassTime());
            dto.setIsInDormitory(cachedStatus.isInDormitory());
            dto.setLastDeviceName(cachedStatus.getLastDeviceName());
            dto.setLastAreaName(cachedStatus.getLastAreaName());
            dto.setLastInOrOutDesc(cachedStatus.getInOrOutDescription());
            dto.setDormitoryStatusDesc(cachedStatus.getStatusDescription());
            
            if (cachedStatus.getLastPassTime() != null) {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                dto.setLastPassTimeStr(cachedStatus.getLastPassTime().format(formatter));
            } else {
                dto.setLastPassTimeStr("无记录");
            }
        } else {
            // 无缓存状态，表示无通行记录
            dto.setLastInOrOut(0);
            dto.setLastPassTime(null);
            dto.setIsInDormitory(false);
            dto.setLastDeviceName("无记录");
            dto.setLastAreaName("无记录");
            dto.setLastInOrOutDesc("无通行记录");
            dto.setDormitoryStatusDesc("无记录");
            dto.setLastPassTimeStr("无记录");
        }
        
        // 基础信息
        dto.setGender(person.getSex());
        dto.setTelephone(person.getTelephone());
        dto.setDormitoryName("");
        
        return dto;
    }

    /**
     * 获取有效人员列表
     * @return 有效人员列表
     */
    @DS("fastgate")
    private List<TblPerson> getActivePersons() {
        return tblPersonRepository.findActivePersons();
    }

    /**
     * 获取有效人员总数
     * @return 有效人员总数
     */
    @DS("fastgate")
    private long getTotalActivePersons() {
        return tblPersonRepository.countActivePersons();
    }

    /**
     * 获取指定楼栋的统计信息（基于已分配宿舍的学生）- 优化版本
     * 优化逻辑：先根据选择的楼栋查出所有关联的学生人数，然后根据这些学生编码去Redis中一个个查询是否在寝室
     * @param buildingCodes 楼栋代码列表
     * @return 汇总统计信息
     */
    public Map<String, Object> getDormitoryStatisticsByBuildings(List<String> buildingCodes) {
        long startTime = System.currentTimeMillis();

        try {
            logger.info("开始获取指定楼栋的统计信息（优化版本）: buildingCodes={}", buildingCodes);

            if (buildingCodes == null || buildingCodes.isEmpty()) {
                logger.info("未指定楼栋，返回全部统计");
                return getDormitoryStatistics();
            }

            // 去重并清理楼栋代码
            Set<String> uniqueBuildingCodes = buildingCodes.stream()
                    .filter(Objects::nonNull)
                    .map(String::trim)
                    .filter(code -> !code.isEmpty())
                    .collect(Collectors.toSet());

            if (uniqueBuildingCodes.isEmpty()) {
                logger.info("楼栋代码清理后为空，返回全部统计");
                return getDormitoryStatistics();
            }

            logger.info("有效楼栋代码: {}", uniqueBuildingCodes);

            // 尝试从缓存获取楼栋统计（如果有缓存的话）
            String cacheKey = "building_stats:" + String.join(",", uniqueBuildingCodes);
            Map<String, Object> cachedResult = tryGetBuildingStatsFromCache(cacheKey);
            if (cachedResult != null) {
                long duration = System.currentTimeMillis() - startTime;
                cachedResult.put("queryMethod", "buildings_filtered_cached");
                cachedResult.put("queryDuration", duration);
                logger.info("从缓存获取楼栋统计成功: 楼栋={}, 耗时={}ms", uniqueBuildingCodes, duration);
                return cachedResult;
            }

            // 缓存未命中，计算楼栋统计
            Map<String, Object> result = calculateBuildingsStatistics(uniqueBuildingCodes);

            // 添加查询元数据
            long duration = System.currentTimeMillis() - startTime;
            result.put("queryMethod", "buildings_filtered_optimized");
            result.put("queryDuration", duration);
            result.put("selectedBuildingCodes", new ArrayList<>(uniqueBuildingCodes));
            result.put("date", getCurrentDate());
            result.put("baseDescription", "基于指定楼栋已分配宿舍学生统计（优化版本）");

            // 缓存结果（短时间缓存，避免重复计算）
            trySetBuildingStatsToCache(cacheKey, result);

            logger.info("指定楼栋统计信息计算完成（优化版本）: 楼栋={}, 耗时={}ms", uniqueBuildingCodes, duration);
            return result;

        } catch (Exception e) {
            logger.error("获取指定楼栋统计信息失败，降级到全部统计: {}", e.getMessage(), e);
            // 降级到全部统计
            Map<String, Object> fallbackResult = getDormitoryStatistics();
            fallbackResult.put("queryMethod", "buildings_filtered_fallback");
            fallbackResult.put("fallbackReason", e.getMessage());
            return fallbackResult;
        }
    }

    /**
     * 尝试从缓存获取楼栋统计（可选实现）
     * @param cacheKey 缓存键
     * @return 缓存的统计结果，如果没有则返回null
     */
    private Map<String, Object> tryGetBuildingStatsFromCache(String cacheKey) {
        try {
            // 这里可以实现楼栋统计的短期缓存逻辑
            // 暂时返回null，表示不使用缓存
            return null;
        } catch (Exception e) {
            logger.debug("获取楼栋统计缓存失败: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 尝试将楼栋统计结果缓存（可选实现）
     * @param cacheKey 缓存键
     * @param result 统计结果
     */
    private void trySetBuildingStatsToCache(String cacheKey, Map<String, Object> result) {
        try {
            // 这里可以实现楼栋统计的短期缓存逻辑
            // 暂时不实现缓存
            logger.debug("楼栋统计结果缓存功能暂未实现");
        } catch (Exception e) {
            logger.debug("缓存楼栋统计结果失败: {}", e.getMessage());
        }
    }

    /**
     * 计算指定楼栋的统计信息（优化版本）
     * 优化逻辑：先根据选择的楼栋查出所有关联的学生人数，然后根据这些学生编码去Redis中一个个查询是否在寝室
     * @param buildingCodes 楼栋代码集合
     * @return 统计结果
     */
    @DS("master")
    private Map<String, Object> calculateBuildingsStatistics(Set<String> buildingCodes) {
        logger.info("开始计算指定楼栋统计（优化版本）: {}", buildingCodes);
        long startTime = System.currentTimeMillis();

        try {
            // 1. 先根据选择的楼栋查出所有关联的学生编码列表（这一步就能得到总人数）
            List<String> buildingStudentCodes = getBuildingStudentCodes(buildingCodes);
            long totalPersons = buildingStudentCodes.size();

            logger.info("楼栋{}共有{}名已分配宿舍的学生", buildingCodes, totalPersons);

            if (totalPersons == 0) {
                logger.warn("指定楼栋中没有已分配宿舍的学生: {}", buildingCodes);
                return createEmptyBuildingStatistics();
            }

            // 2. 根据这些学生编码去Redis中逐个查询是否在寝室来判断是否在寝室或者无记录
            Map<String, Object> statusStats = analyzeStudentStatusFromRedis(buildingStudentCodes);

            // 3. 组装最终统计结果
            Map<String, Object> result = new HashMap<>();
            result.put("totalPersons", totalPersons);
            result.put("returnedCount", statusStats.getOrDefault("returnedCount", 0L));
            result.put("notReturnedCount", statusStats.getOrDefault("notReturnedCount", 0L));
            result.put("personsWithoutRecords", statusStats.getOrDefault("personsWithoutRecords", 0L));

            // 计算归寝率
            long returnedCount = ((Number) statusStats.getOrDefault("returnedCount", 0L)).longValue();
            double returnRate = totalPersons > 0 ? (returnedCount * 100.0 / totalPersons) : 0.0;
            result.put("returnRate", Math.round(returnRate * 100.0) / 100.0);

            long duration = System.currentTimeMillis() - startTime;
            result.put("calculationDuration", duration);

            logger.info("楼栋统计完成: 楼栋={}, 总人数={}, 在寝={}, 外出={}, 无记录={}, 归寝率={}%, 耗时={}ms",
                       buildingCodes, totalPersons, result.get("returnedCount"), result.get("notReturnedCount"),
                       result.get("personsWithoutRecords"), result.get("returnRate"), duration);

            return result;

        } catch (Exception e) {
            logger.error("计算楼栋统计失败: buildingCodes={}, error={}", buildingCodes, e.getMessage(), e);
            // 降级到数据库查询
            return calculateBuildingsStatusFromDB(buildingCodes);
        }
    }

    /**
     * 创建空的楼栋统计结果
     * @return 空的楼栋统计结果
     */
    private Map<String, Object> createEmptyBuildingStatistics() {
        Map<String, Object> result = new HashMap<>();
        result.put("totalPersons", 0L);
        result.put("returnedCount", 0L);
        result.put("notReturnedCount", 0L);
        result.put("personsWithoutRecords", 0L);
        result.put("returnRate", 0.0);
        return result;
    }

    /**
     * 计算指定楼栋学生的状态统计（优化版本）
     * 优化逻辑：先根据选择的楼栋查出所有关联的学生人数，然后根据这些学生编码去Redis中一个个查询是否在寝室
     * @param buildingCodes 楼栋代码集合
     * @return 状态统计结果
     */
    @DS("master")
    private Map<String, Object> calculateBuildingsStatusStats(Set<String> buildingCodes) {
        logger.info("开始计算指定楼栋学生状态统计（优化版本）: {}", buildingCodes);
        long startTime = System.currentTimeMillis();

        try {
            // 1. 先根据选择的楼栋查出所有关联的学生编码列表
            List<String> buildingStudentCodes = getBuildingStudentCodes(buildingCodes);

            if (buildingStudentCodes.isEmpty()) {
                logger.warn("指定楼栋中没有学生: {}", buildingCodes);
                return createEmptyStatusResult();
            }

            logger.info("指定楼栋{}共有{}名学生", buildingCodes, buildingStudentCodes.size());

            // 2. 根据这些学生编码去Redis中逐个查询是否在寝室
            Map<String, Object> statusStats = analyzeStudentStatusFromRedis(buildingStudentCodes);

            long duration = System.currentTimeMillis() - startTime;
            logger.info("楼栋学生状态统计完成: 楼栋={}, 学生数={}, 在寝={}, 外出={}, 无记录={}, 耗时={}ms",
                    buildingCodes, buildingStudentCodes.size(),
                    statusStats.get("returnedCount"), statusStats.get("notReturnedCount"),
                    statusStats.get("personsWithoutRecords"), duration);

            return statusStats;

        } catch (Exception e) {
            logger.error("计算楼栋学生状态统计失败: buildingCodes={}, error={}", buildingCodes, e.getMessage(), e);
            // 降级到数据库查询
            return calculateBuildingsStatusFromDB(buildingCodes);
        }
    }

    /**
     * 获取指定楼栋的学生编码列表
     * @param buildingCodes 楼栋代码集合
     * @return 学生编码列表
     */
    @DS("master")
    private List<String> getBuildingStudentCodes(Set<String> buildingCodes) {
        logger.debug("查询楼栋{}的学生编码列表", buildingCodes);

        // 使用优化的SQL查询，直接从person_dormitory_relation和dormitory_info表关联查询
        String studentCodesSql = "SELECT DISTINCT pdr.person_code " +
                "FROM person_dormitory_relation pdr " +
                "INNER JOIN dormitory_info di ON pdr.dormitory_code = di.dormitory_code " +
                "INNER JOIN person_info pi ON pdr.person_code = pi.person_code " +
                "WHERE pdr.status = 1 " +
                "AND di.status = 1 " +
                "AND pi.status = 1 " +
                "AND di.building_code IN (:buildingCodes) " +
                "ORDER BY pdr.person_code";

        Query studentQuery = entityManager.createNativeQuery(studentCodesSql);
        studentQuery.setParameter("buildingCodes", buildingCodes);

        @SuppressWarnings("unchecked")
        List<String> studentCodes = studentQuery.getResultList();

        logger.debug("楼栋{}查询到{}名学生", buildingCodes, studentCodes.size());
        return studentCodes;
    }

    /**
     * 根据学生编码列表从Redis中逐个查询状态并统计（优化版本）
     * 优化逻辑：根据这些学生编码去Redis中一个个查询是否在寝室来判断是否在寝室或者无记录
     * @param studentCodes 学生编码列表
     * @return 状态统计结果
     */
    private Map<String, Object> analyzeStudentStatusFromRedis(List<String> studentCodes) {
        logger.info("开始从Redis逐个查询{}名学生的状态（优化版本）", studentCodes.size());
        long startTime = System.currentTimeMillis();

        long returnedCount = 0;      // 在寝人数
        long notReturnedCount = 0;   // 外出人数
        long personsWithoutRecords = 0; // 无记录人数

        // 性能统计
        long redisQueryTime = 0;
        long analysisTime = 0;
        int redisHitCount = 0;
        int redisMissCount = 0;

        // 分批处理，避免一次性处理过多数据，提高Redis查询效率
        int batchSize = 500; // 每批处理500个学生
        int totalBatches = (int) Math.ceil((double) studentCodes.size() / batchSize);

        logger.info("将分{}批处理学生状态查询，每批{}个学生", totalBatches, batchSize);

        for (int batchIndex = 0; batchIndex < totalBatches; batchIndex++) {
            int startIndex = batchIndex * batchSize;
            int endIndex = Math.min(startIndex + batchSize, studentCodes.size());
            List<String> batchStudentCodes = studentCodes.subList(startIndex, endIndex);

            logger.debug("处理第{}/{}批学生状态查询，学生数: {}", batchIndex + 1, totalBatches, batchStudentCodes.size());

            // 批量从Redis获取学生状态
            long batchRedisStart = System.currentTimeMillis();
            Map<String, StudentStatusCacheDTO> batchStatusMap = statusCacheService.batchGetStudentStatus(batchStudentCodes);
            redisQueryTime += (System.currentTimeMillis() - batchRedisStart);

            // 逐个分析学生状态
            long batchAnalysisStart = System.currentTimeMillis();
            for (String studentCode : batchStudentCodes) {
                StudentStatusCacheDTO status = batchStatusMap.get(studentCode);

                if (status == null || status.getLastPassTime() == null) {
                    // 没有缓存记录或没有通行记录 = 无记录
                    personsWithoutRecords++;
                    redisMissCount++;
                    logger.trace("学生{}无通行记录", studentCode);
                } else {
                    redisHitCount++;
                    if (status.isInDormitory()) {
                        // 在寝
                        returnedCount++;
                        logger.trace("学生{}在寝室", studentCode);
                    } else {
                        // 外出
                        notReturnedCount++;
                        logger.trace("学生{}外出", studentCode);
                    }
                }
            }
            analysisTime += (System.currentTimeMillis() - batchAnalysisStart);

            // 每处理完一批，记录进度
            if (batchIndex % 5 == 0 || batchIndex == totalBatches - 1) {
                logger.debug("已处理{}/{}批，当前统计: 在寝={}, 外出={}, 无记录={}",
                        batchIndex + 1, totalBatches, returnedCount, notReturnedCount, personsWithoutRecords);
            }
        }

        long totalDuration = System.currentTimeMillis() - startTime;

        Map<String, Object> result = new HashMap<>();
        result.put("returnedCount", returnedCount);
        result.put("notReturnedCount", notReturnedCount);
        result.put("personsWithoutRecords", personsWithoutRecords);

        // 添加性能统计信息
        Map<String, Object> performanceStats = new HashMap<>();
        performanceStats.put("totalDuration", totalDuration);
        performanceStats.put("redisQueryTime", redisQueryTime);
        performanceStats.put("analysisTime", analysisTime);
        performanceStats.put("redisHitCount", redisHitCount);
        performanceStats.put("redisMissCount", redisMissCount);
        performanceStats.put("redisHitRate", redisHitCount + redisMissCount > 0 ?
                (double) redisHitCount / (redisHitCount + redisMissCount) * 100 : 0.0);
        result.put("performanceStats", performanceStats);

        logger.info("Redis状态查询完成: 在寝={}, 外出={}, 无记录={}, Redis命中率={}%, 总耗时={}ms, Redis查询={}ms, 分析={}ms",
                returnedCount, notReturnedCount, personsWithoutRecords,
                redisHitCount + redisMissCount > 0 ? (double) redisHitCount / (redisHitCount + redisMissCount) * 100 : 0.0,
                totalDuration, redisQueryTime, analysisTime);

        return result;
    }

    /**
     * 创建空的状态统计结果
     * @return 空的状态统计结果
     */
    private Map<String, Object> createEmptyStatusResult() {
        Map<String, Object> result = new HashMap<>();
        result.put("returnedCount", 0L);
        result.put("notReturnedCount", 0L);
        result.put("personsWithoutRecords", 0L);
        return result;
    }

    /**
     * 从数据库计算指定楼栋学生的状态统计（降级方案）
     * @param buildingCodes 楼栋代码集合
     * @return 状态统计结果
     */
    @DS("fastgate")
    private Map<String, Object> calculateBuildingsStatusFromDB(Set<String> buildingCodes) {
        logger.info("从数据库计算楼栋状态统计: {}", buildingCodes);
        
        // 使用原始查询逻辑，但添加楼栋筛选条件
        // 这里简化实现，可以复用原有的统计逻辑
        long returnedCount = 0;
        long notReturnedCount = 0;
        long personsWithoutRecords = 0;
        
        // 实际实现中应该调用原有的统计服务，并添加楼栋筛选
        // 这里暂时返回默认值，具体实现可以参考原始的getDormitoryStatistics方法
        
        Map<String, Object> result = new HashMap<>();
        result.put("returnedCount", returnedCount);
        result.put("notReturnedCount", notReturnedCount);
        result.put("personsWithoutRecords", personsWithoutRecords);
        
        return result;
    }

    /**
     * 获取当前日期
     * @return 当前日期字符串
     */
    private String getCurrentDate() {
        return java.time.LocalDate.now().format(java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd"));
    }
}