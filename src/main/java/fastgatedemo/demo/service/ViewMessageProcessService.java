package fastgatedemo.demo.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import fastgatedemo.demo.dto.AccessRecordDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 视图消息处理服务类
 * 负责解析JSON消息并更新学生在寝状态
 */
@Service
public class ViewMessageProcessService {

    private static final Logger logger = LoggerFactory.getLogger(ViewMessageProcessService.class);

    @Autowired
    private StudentStatusCacheService statusCacheService;

    @Autowired
    private AsyncBatchPersistenceService persistenceService;

    /**
     * 处理接收到的消息
     * @param messageJson JSON消息字符串
     * @return 处理结果
     */
    public boolean processMessage(String messageJson) {
        try {
            logger.info("开始处理消息: {}", messageJson);
            
            JSONObject jsonObject = JSON.parseObject(messageJson);
            boolean hasProcessedRecord = false;
            
            // 检查是否包含PersonCardObjectList
            if (jsonObject.containsKey("PersonCardObjectList")) {
                JSONArray personCardList = jsonObject.getJSONArray("PersonCardObjectList");
                if (personCardList != null && !personCardList.isEmpty()) {
                    logger.info("发现PersonCardObjectList，包含{}条记录", personCardList.size());
                    for (int i = 0; i < personCardList.size(); i++) {
                        JSONObject personCard = personCardList.getJSONObject(i);
                        if (processPersonCardRecord(personCard)) {
                            hasProcessedRecord = true;
                        }
                    }
                }
            }
            
            // 检查是否包含PersonCheckObjectList  
            if (jsonObject.containsKey("PersonCheckObjectList")) {
                JSONArray personCheckList = jsonObject.getJSONArray("PersonCheckObjectList");
                if (personCheckList != null && !personCheckList.isEmpty()) {
                    logger.info("发现PersonCheckObjectList，包含{}条记录", personCheckList.size());
                    for (int i = 0; i < personCheckList.size(); i++) {
                        JSONObject personCheck = personCheckList.getJSONObject(i);
                        if (processPersonCheckRecord(personCheck)) {
                            hasProcessedRecord = true;
                        }
                    }
                }
            }
            
            // 如果没有找到预期的数据结构，尝试直接解析
            if (!hasProcessedRecord) {
                logger.info("未找到标准数据结构，尝试直接解析消息");
                hasProcessedRecord = processDirectMessage(jsonObject);
            }
            
            return hasProcessedRecord;
            
        } catch (Exception e) {
            logger.error("处理消息时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理PersonCard记录
     */
    private boolean processPersonCardRecord(JSONObject personCard) {
        try {
            // 提取通行记录信息
            AccessRecordDTO accessRecord = extractAccessRecord(personCard, "PersonCard");
            
            if (accessRecord != null && isValidAccessRecord(accessRecord)) {
                return processAccessRecord(accessRecord);
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("处理PersonCard记录时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理PersonCheck记录
     */
    private boolean processPersonCheckRecord(JSONObject personCheck) {
        try {
            // 提取通行记录信息
            AccessRecordDTO accessRecord = extractAccessRecord(personCheck, "PersonCheck");
            
            if (accessRecord != null && isValidAccessRecord(accessRecord)) {
                return processAccessRecord(accessRecord);
            }
            
            return false;
            
        } catch (Exception e) {
            logger.error("处理PersonCheck记录时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 直接处理消息
     */
    private boolean processDirectMessage(JSONObject jsonObject) {
        try {
            // 尝试从直接消息中提取通行记录
            AccessRecordDTO accessRecord = extractAccessRecord(jsonObject, "Direct");
            
            if (accessRecord != null && isValidAccessRecord(accessRecord)) {
                return processAccessRecord(accessRecord);
            }
            
            logger.info("直接消息未包含有效的通行记录信息");
            return false;
            
        } catch (Exception e) {
            logger.error("直接处理消息时发生错误: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 从JSON对象中提取通行记录
     * @param jsonObject JSON对象
     * @param recordType 记录类型
     * @return 通行记录DTO
     */
    private AccessRecordDTO extractAccessRecord(JSONObject jsonObject, String recordType) {
        try {
            AccessRecordDTO accessRecord = new AccessRecordDTO();
            
            // 基础信息提取
            accessRecord.setPersonCode(getStringValue(jsonObject, "PersonCode", "personCode", "person_code"));
            accessRecord.setPersonName(getStringValue(jsonObject, "Name", "name", "PersonName"));
            accessRecord.setDeviceCode(getStringValue(jsonObject, "DeviceID", "deviceId", "device_id"));
            accessRecord.setDeviceName(getStringValue(jsonObject, "DeviceName", "deviceName", "device_name"));
            accessRecord.setPlaceCode(getStringValue(jsonObject, "PlaceCode", "placeCode", "place_code"));
            accessRecord.setPlaceName(getStringValue(jsonObject, "PlaceName", "placeName", "place_name"));
            accessRecord.setAreaCode(getStringValue(jsonObject, "AreaCode", "areaCode", "area_code"));
            accessRecord.setAreaName(getStringValue(jsonObject, "AreaName", "areaName", "area_name"));
            
            // 通行方向 - 关键字段
            Integer inOrOut = getIntegerValue(jsonObject, "InOrOut", "inorout", "Direction", "direction");
            accessRecord.setInOrOut(inOrOut);
            
            // 时间信息提取
            LocalDateTime passTime = extractPassTime(jsonObject);
            accessRecord.setPassTime(passTime);
            
            if (passTime != null) {
                DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
                DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");
                accessRecord.setRecordDate(passTime.format(dateFormatter));
                accessRecord.setRecordTime(passTime.format(timeFormatter));
            }
            
            // 其他信息
            accessRecord.setMatchConfidence(getIntegerValue(jsonObject, "MatchConfidence", "matchConfidence", "match_confidence"));
            accessRecord.setDeviceIp(getStringValue(jsonObject, "DeviceIP", "deviceIp", "device_ip"));
            accessRecord.setTemperature(getDoubleValue(jsonObject, "Temperature", "temperature"));
            
            logger.debug("提取通行记录[{}]: personCode={}, inOrOut={}, passTime={}", 
                        recordType, accessRecord.getPersonCode(), accessRecord.getInOrOut(), accessRecord.getPassTime());
            
            return accessRecord;
            
        } catch (Exception e) {
            logger.error("提取通行记录失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 提取通行时间
     * @param jsonObject JSON对象
     * @return 通行时间
     */
    private LocalDateTime extractPassTime(JSONObject jsonObject) {
        try {
            // 尝试多种时间字段名称
            String timeStr = getStringValue(jsonObject, "CapTime", "CheckTime", "PassTime", 
                                          "capTime", "checkTime", "passTime", "pass_time");
            
            if (timeStr != null && !timeStr.trim().isEmpty()) {
                // 尝试解析多种时间格式
                return parseDateTime(timeStr.trim());
            }
            
            // 如果没有时间字段，使用当前时间
            return LocalDateTime.now();
            
        } catch (Exception e) {
            logger.warn("解析通行时间失败，使用当前时间: {}", e.getMessage());
            return LocalDateTime.now();
        }
    }

    /**
     * 解析日期时间字符串
     * @param timeStr 时间字符串
     * @return LocalDateTime
     */
    private LocalDateTime parseDateTime(String timeStr) {
        // 常见的时间格式
        String[] patterns = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss",
            "yyyy-MM-dd'T'HH:mm:ss.SSS",
            "yyyy/MM/dd HH:mm:ss",
            "yyyyMMdd HH:mm:ss",
            "yyyyMMddHHmmss"
        };
        
        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                return LocalDateTime.parse(timeStr, formatter);
            } catch (Exception ignored) {
                // 继续尝试下一种格式
            }
        }
        
        throw new RuntimeException("无法解析时间格式: " + timeStr);
    }

    /**
     * 验证通行记录是否有效
     * @param accessRecord 通行记录
     * @return 是否有效
     */
    private boolean isValidAccessRecord(AccessRecordDTO accessRecord) {
        // 必须有人员编码
        if (accessRecord.getPersonCode() == null || accessRecord.getPersonCode().trim().isEmpty()) {
            logger.warn("通行记录缺少人员编码，忽略处理");
            return false;
        }
        
        // 必须有进出方向
        if (accessRecord.getInOrOut() == null) {
            logger.warn("通行记录缺少进出方向，忽略处理: personCode={}", accessRecord.getPersonCode());
            return false;
        }
        
        // 进出方向必须是1或2
        if (accessRecord.getInOrOut() != 1 && accessRecord.getInOrOut() != 2) {
            logger.warn("通行记录进出方向无效({}，应为1或2)，忽略处理: personCode={}", 
                       accessRecord.getInOrOut(), accessRecord.getPersonCode());
            return false;
        }
        
        return true;
    }

    /**
     * 处理通行记录 - 核心业务逻辑
     * @param accessRecord 通行记录
     * @return 处理是否成功
     */
    private boolean processAccessRecord(AccessRecordDTO accessRecord) {
        try {
            logger.info("处理通行记录: personCode={}, personName={}, inOrOut={}, passTime={}", 
                       accessRecord.getPersonCode(), accessRecord.getPersonName(), 
                       accessRecord.getInOrOut(), accessRecord.getPassTime());
            
            // 1. 立即更新Redis缓存中的学生状态
            boolean cacheUpdated = statusCacheService.updateStudentStatus(accessRecord);
            if (!cacheUpdated) {
                logger.warn("更新学生状态缓存失败: personCode={}", accessRecord.getPersonCode());
            }
            
            // 2. 添加到异步持久化队列
            boolean queueAdded = persistenceService.addToQueue(accessRecord);
            if (!queueAdded) {
                logger.warn("添加到持久化队列失败: personCode={}", accessRecord.getPersonCode());
            }
            
            // 只要缓存更新成功就认为处理成功（因为缓存是查询的主要数据源）
            if (cacheUpdated) {
                logger.info("通行记录处理成功: personCode={}, 状态={}", 
                           accessRecord.getPersonCode(), accessRecord.getInOrOutDescription());
                return true;
            } else {
                logger.error("通行记录处理失败: personCode={}", accessRecord.getPersonCode());
                return false;
            }
            
        } catch (Exception e) {
            logger.error("处理通行记录异常: personCode={}, error={}", 
                        accessRecord.getPersonCode(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 获取字符串值（支持多个可能的字段名）
     */
    private String getStringValue(JSONObject json, String... fieldNames) {
        for (String fieldName : fieldNames) {
            if (json.containsKey(fieldName)) {
                String value = json.getString(fieldName);
                if (value != null && !value.trim().isEmpty()) {
                    return value.trim();
                }
            }
        }
        return null;
    }

    /**
     * 获取整数值（支持多个可能的字段名）
     */
    private Integer getIntegerValue(JSONObject json, String... fieldNames) {
        for (String fieldName : fieldNames) {
            if (json.containsKey(fieldName)) {
                Object value = json.get(fieldName);
                if (value != null) {
                    try {
                        if (value instanceof Number) {
                            return ((Number) value).intValue();
                        } else {
                            return Integer.parseInt(value.toString());
                        }
                    } catch (NumberFormatException e) {
                        logger.warn("无法解析整数值: {}={}", fieldName, value);
                    }
                }
            }
        }
        return null;
    }

    /**
     * 获取双精度值（支持多个可能的字段名）
     */
    private Double getDoubleValue(JSONObject json, String... fieldNames) {
        for (String fieldName : fieldNames) {
            if (json.containsKey(fieldName)) {
                Object value = json.get(fieldName);
                if (value != null) {
                    try {
                        if (value instanceof Number) {
                            return ((Number) value).doubleValue();
                        } else {
                            return Double.parseDouble(value.toString());
                        }
                    } catch (NumberFormatException e) {
                        logger.warn("无法解析双精度值: {}={}", fieldName, value);
                    }
                }
            }
        }
        return null;
    }
}