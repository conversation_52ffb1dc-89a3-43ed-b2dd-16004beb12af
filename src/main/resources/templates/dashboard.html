<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>宿舍管理大屏</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', sans-serif;
            min-height: 100vh;
        }
        
        .dashboard-container {
            padding: 20px;
        }
        
        .stats-card {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .stats-card:hover {
            transform: translateY(-5px);
        }
        
        .stats-number {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 0;
        }
        
        .stats-label {
            font-size: 1rem;
            color: #6c757d;
            margin-bottom: 0;
        }
        
        .building-selector {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .building-checkbox {
            margin-right: 15px;
            margin-bottom: 10px;
        }
        
        .building-checkbox input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }
        
        .building-checkbox label {
            font-weight: 500;
            cursor: pointer;
            padding: 5px 10px;
            border-radius: 5px;
            transition: background-color 0.2s;
        }
        
        .building-checkbox label:hover {
            background-color: rgba(0, 123, 255, 0.1);
        }
        
        .action-buttons {
            margin-top: 15px;
        }
        
        .performance-info {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 10px;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            background: white;
            padding: 30px;
            border-radius: 10px;
            text-align: center;
        }
        
        .update-time {
            font-size: 0.9rem;
            color: #6c757d;
            text-align: center;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-spinner fa-spin fa-2x text-primary mb-3"></i>
            <div>正在查询数据...</div>
        </div>
    </div>

    <div class="dashboard-container">
        <!-- 标题 -->
        <div class="row mb-4">
            <div class="col-12 text-center">
                <h1 class="text-white mb-0">
                    <i class="fas fa-home"></i> 宿舍管理大屏
                </h1>
                <p class="text-white-50 mt-2">实时监控学生归寝情况</p>
            </div>
        </div>

        <!-- 楼栋选择器 -->
        <div class="building-selector">
            <h5 class="mb-3">
                <i class="fas fa-building text-primary"></i> 楼栋选择
            </h5>
            
            <!-- 楼栋复选框区域 -->
            <div id="buildingCheckboxes" class="row">
                <div class="col-12 text-center text-muted">
                    <i class="fas fa-spinner fa-spin"></i> 正在加载楼栋列表...
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="action-buttons">
                <button type="button" class="btn btn-primary" onclick="querySelectedBuildings()" id="queryBtn">
                    <i class="fas fa-search"></i> 查询选中楼栋
                </button>
                <button type="button" class="btn btn-success" onclick="queryAllBuildings()">
                    <i class="fas fa-home"></i> 查询全部
                </button>
                <button type="button" class="btn btn-warning" onclick="refreshData()">
                    <i class="fas fa-sync-alt"></i> 刷新数据
                </button>
                <button type="button" class="btn btn-secondary" onclick="selectAllBuildings()">
                    <i class="fas fa-check-square"></i> 全选
                </button>
                <button type="button" class="btn btn-outline-secondary" onclick="clearAllBuildings()">
                    <i class="fas fa-square"></i> 清空
                </button>
            </div>
            
            <!-- 选择状态提示 -->
            <div class="mt-2">
                <small class="text-muted">
                    已选择 <span id="selectedCount" class="text-primary font-weight-bold">0</span> 个楼栋
                </small>
            </div>
        </div>

        <!-- 统计数据卡片 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card text-center p-4">
                    <div class="stats-number text-primary" id="totalPersons">-</div>
                    <div class="stats-label">总人数</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card text-center p-4">
                    <div class="stats-number text-success" id="inDormitoryPersons">-</div>
                    <div class="stats-label">在寝人员</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card text-center p-4">
                    <div class="stats-number text-warning" id="outDormitoryPersons">-</div>
                    <div class="stats-label">外出人员</div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="stats-card text-center p-4">
                    <div class="stats-number text-info" id="returnRate">-</div>
                    <div class="stats-label">归寝率</div>
                </div>
            </div>
        </div>

        <!-- 详细信息 -->
        <div class="row">
            <div class="col-12">
                <div class="stats-card p-4">
                    <h5 class="mb-3">
                        <i class="fas fa-chart-bar text-primary"></i> 详细统计
                    </h5>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="d-flex justify-content-between">
                                <span>无记录人员：</span>
                                <span class="text-muted" id="personsWithoutRecords">-</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex justify-content-between">
                                <span>数据来源：</span>
                                <span class="text-muted" id="dataSource">-</span>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="d-flex justify-content-between">
                                <span>查询方法：</span>
                                <span class="text-muted" id="queryMethod">-</span>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 性能信息 -->
                    <div class="performance-info mt-3" id="performanceInfo" style="display: none;">
                        <h6>性能统计：</h6>
                        <div class="row">
                            <div class="col-md-3">
                                <small>查询耗时: <span id="queryDuration">-</span>ms</small>
                            </div>
                            <div class="col-md-3">
                                <small>Redis耗时: <span id="redisQueryTime">-</span>ms</small>
                            </div>
                            <div class="col-md-3">
                                <small>命中率: <span id="redisHitRate">-</span>%</small>
                            </div>
                            <div class="col-md-3">
                                <small>处理速度: <span id="studentsPerSecond">-</span>/秒</small>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 更新时间 -->
                    <div class="update-time" id="updateTime">
                        最后更新: -
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let availableBuildings = [];
        let isQuerying = false;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('大屏页面初始化');
            loadBuildingList();
            queryAllBuildings(); // 默认查询全部数据
        });

        // 加载楼栋列表
        function loadBuildingList() {
            console.log('开始加载楼栋列表');
            
            fetch('/dormitory/buildings', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('楼栋列表响应:', data);
                
                if (data.ErrCode === 200 && data.data) {
                    availableBuildings = data.data;
                    displayBuildingCheckboxes(availableBuildings);
                } else {
                    document.getElementById('buildingCheckboxes').innerHTML = 
                        '<div class="col-12 text-center text-danger">加载楼栋列表失败: ' + (data.ErrMsg || '未知错误') + '</div>';
                }
            })
            .catch(error => {
                console.error('加载楼栋列表错误:', error);
                document.getElementById('buildingCheckboxes').innerHTML = 
                    '<div class="col-12 text-center text-danger">网络错误，无法加载楼栋列表</div>';
            });
        }

        // 显示楼栋复选框
        function displayBuildingCheckboxes(buildings) {
            const container = document.getElementById('buildingCheckboxes');

            if (!buildings || buildings.length === 0) {
                container.innerHTML = '<div class="col-12 text-center text-muted">暂无楼栋数据</div>';
                return;
            }

            let html = '';
            buildings.forEach(building => {
                html += `
                    <div class="col-lg-2 col-md-3 col-sm-4 col-6">
                        <div class="building-checkbox">
                            <input type="checkbox" id="building_${building.buildingCode}"
                                   value="${building.buildingCode}" onchange="updateSelectedCount()">
                            <label for="building_${building.buildingCode}">
                                ${building.buildingName || building.buildingCode}
                            </label>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
            updateSelectedCount();
        }

        // 更新选中数量
        function updateSelectedCount() {
            const checkboxes = document.querySelectorAll('#buildingCheckboxes input[type="checkbox"]');
            const selectedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            document.getElementById('selectedCount').textContent = selectedCount;

            // 控制查询按钮状态
            const queryBtn = document.getElementById('queryBtn');
            if (selectedCount > 0) {
                queryBtn.disabled = false;
                queryBtn.innerHTML = `<i class="fas fa-search"></i> 查询选中楼栋 (${selectedCount})`;
            } else {
                queryBtn.disabled = true;
                queryBtn.innerHTML = '<i class="fas fa-search"></i> 查询选中楼栋';
            }
        }

        // 全选楼栋
        function selectAllBuildings() {
            const checkboxes = document.querySelectorAll('#buildingCheckboxes input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = true);
            updateSelectedCount();
        }

        // 清空选择
        function clearAllBuildings() {
            const checkboxes = document.querySelectorAll('#buildingCheckboxes input[type="checkbox"]');
            checkboxes.forEach(cb => cb.checked = false);
            updateSelectedCount();
        }

        // 获取选中的楼栋代码
        function getSelectedBuildingCodes() {
            const checkboxes = document.querySelectorAll('#buildingCheckboxes input[type="checkbox"]:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 显示加载状态
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
            isQuerying = true;
        }

        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
            isQuerying = false;
        }

        // 查询选中楼栋数据
        function querySelectedBuildings() {
            if (isQuerying) return;

            const selectedCodes = getSelectedBuildingCodes();
            if (selectedCodes.length === 0) {
                alert('请先选择要查询的楼栋');
                return;
            }

            console.log('查询选中楼栋:', selectedCodes);
            queryDashboardData(selectedCodes, false);
        }

        // 查询全部楼栋数据
        function queryAllBuildings() {
            if (isQuerying) return;

            console.log('查询全部楼栋数据');
            queryDashboardData(null, false);
        }

        // 刷新数据
        function refreshData() {
            if (isQuerying) return;

            const selectedCodes = getSelectedBuildingCodes();
            console.log('刷新数据:', selectedCodes.length > 0 ? selectedCodes : '全部');
            queryDashboardData(selectedCodes.length > 0 ? selectedCodes : null, true);
        }

        // 统一的数据查询方法（调用优化后的统一接口）
        function queryDashboardData(buildingCodes, forceRefresh) {
            showLoading();

            const requestBody = {};
            if (buildingCodes && buildingCodes.length > 0) {
                requestBody.buildingCodes = buildingCodes;
            }
            if (forceRefresh) {
                requestBody.refresh = true;
            }

            console.log('调用统一查询接口:', requestBody);

            fetch('/api/dashboard/query', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestBody)
            })
            .then(response => response.json())
            .then(data => {
                console.log('查询响应:', data);

                if (data.success && data.data) {
                    updateDashboardDisplay(data.data);
                } else {
                    alert('查询失败: ' + (data.message || '未知错误'));
                    console.error('查询失败:', data);
                }
            })
            .catch(error => {
                console.error('查询错误:', error);
                alert('网络错误，查询失败: ' + error.message);
            })
            .finally(() => {
                hideLoading();
            });
        }

        // 更新大屏显示
        function updateDashboardDisplay(data) {
            console.log('更新大屏显示:', data);

            // 更新主要统计数据
            document.getElementById('totalPersons').textContent = data.totalPersons || 0;
            document.getElementById('inDormitoryPersons').textContent = data.inDormitoryPersons || 0;
            document.getElementById('outDormitoryPersons').textContent = data.outDormitoryPersons || 0;
            document.getElementById('returnRate').textContent = (data.returnRate || 0) + '%';

            // 更新详细信息
            document.getElementById('personsWithoutRecords').textContent = data.personsWithoutRecords || 0;
            document.getElementById('dataSource').textContent = getDataSourceText(data.dataSource);
            document.getElementById('queryMethod').textContent = data.queryMethod || '-';
            document.getElementById('updateTime').textContent = '最后更新: ' + (data.updateTime || '-');

            // 更新性能信息
            if (data.performanceStats) {
                updatePerformanceInfo(data.performanceStats, data.queryDuration);
            } else {
                document.getElementById('performanceInfo').style.display = 'none';
            }

            // 显示选中的楼栋信息
            if (data.selectedBuildings && data.selectedBuildings.length > 0) {
                console.log('当前查询楼栋:', data.selectedBuildings);
            }
        }

        // 获取数据源描述文本
        function getDataSourceText(dataSource) {
            const sourceMap = {
                'all_buildings': '全部楼栋',
                'selected_buildings': '选中楼栋',
                'force_refreshed': '强制刷新',
                'buildings_filtered_optimized': '楼栋筛选（优化）',
                'assigned_dormitory_based': '已分配宿舍',
                'error': '查询错误'
            };
            return sourceMap[dataSource] || dataSource || '-';
        }

        // 更新性能信息显示
        function updatePerformanceInfo(perfStats, queryDuration) {
            document.getElementById('queryDuration').textContent = queryDuration || perfStats.totalDuration || '-';
            document.getElementById('redisQueryTime').textContent = perfStats.redisQueryTime || '-';
            document.getElementById('redisHitRate').textContent = perfStats.redisHitRate ?
                Math.round(perfStats.redisHitRate * 100) / 100 : '-';
            document.getElementById('studentsPerSecond').textContent = perfStats.studentsPerSecond ?
                Math.round(perfStats.studentsPerSecond) : '-';

            document.getElementById('performanceInfo').style.display = 'block';
        }

        // 定时刷新数据（可选）
        function startAutoRefresh(intervalMinutes = 5) {
            setInterval(() => {
                if (!isQuerying) {
                    console.log('定时自动刷新数据');
                    const selectedCodes = getSelectedBuildingCodes();
                    queryDashboardData(selectedCodes.length > 0 ? selectedCodes : null, false);
                }
            }, intervalMinutes * 60 * 1000);
        }

        // 启动定时刷新（5分钟）
        // startAutoRefresh(5);
    </script>
</body>
</html>
