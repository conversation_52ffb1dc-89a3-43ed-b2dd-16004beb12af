package fastgatedemo.demo.service;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * @description 大屏查询接口优化测试
 * 测试优化后的楼栋查询性能
 * <AUTHOR>
 * @date 2025-01-30
 */
@SpringBootTest
@ActiveProfiles("test")
public class CachedDormitoryStatsServiceOptimizationTest {

    private static final Logger logger = LoggerFactory.getLogger(CachedDormitoryStatsServiceOptimizationTest.class);

    @Autowired
    private CachedDormitoryStatsService cachedDormitoryStatsService;

    /**
     * 测试单个楼栋查询优化效果
     */
    @Test
    public void testSingleBuildingQueryOptimization() {
        logger.info("=== 测试单个楼栋查询优化效果 ===");
        
        List<String> buildingCodes = Arrays.asList("A1");
        
        // 执行多次查询测试性能
        for (int i = 1; i <= 3; i++) {
            logger.info("第{}次查询开始", i);
            long startTime = System.currentTimeMillis();
            
            Map<String, Object> result = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(buildingCodes);
            
            long duration = System.currentTimeMillis() - startTime;
            
            logger.info("第{}次查询结果: 楼栋={}, 总人数={}, 在寝={}, 外出={}, 无记录={}, 归寝率={}%, 耗时={}ms", 
                    i, buildingCodes, 
                    result.get("totalPersons"), 
                    result.get("returnedCount"), 
                    result.get("notReturnedCount"), 
                    result.get("personsWithoutRecords"), 
                    result.get("returnRate"), 
                    duration);
            
            if (result.containsKey("performanceStats")) {
                logger.info("第{}次查询性能统计: {}", i, result.get("performanceStats"));
            }
            
            logger.info("第{}次查询方法: {}", i, result.get("queryMethod"));
        }
    }

    /**
     * 测试多个楼栋查询优化效果
     */
    @Test
    public void testMultipleBuildingsQueryOptimization() {
        logger.info("=== 测试多个楼栋查询优化效果 ===");
        
        List<String> buildingCodes = Arrays.asList("A1", "A2", "B1");
        
        // 执行多次查询测试性能
        for (int i = 1; i <= 3; i++) {
            logger.info("第{}次多楼栋查询开始", i);
            long startTime = System.currentTimeMillis();
            
            Map<String, Object> result = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(buildingCodes);
            
            long duration = System.currentTimeMillis() - startTime;
            
            logger.info("第{}次多楼栋查询结果: 楼栋={}, 总人数={}, 在寝={}, 外出={}, 无记录={}, 归寝率={}%, 耗时={}ms", 
                    i, buildingCodes, 
                    result.get("totalPersons"), 
                    result.get("returnedCount"), 
                    result.get("notReturnedCount"), 
                    result.get("personsWithoutRecords"), 
                    result.get("returnRate"), 
                    duration);
            
            if (result.containsKey("performanceStats")) {
                logger.info("第{}次多楼栋查询性能统计: {}", i, result.get("performanceStats"));
            }
            
            logger.info("第{}次多楼栋查询方法: {}", i, result.get("queryMethod"));
        }
    }

    /**
     * 测试空楼栋列表的处理
     */
    @Test
    public void testEmptyBuildingCodesHandling() {
        logger.info("=== 测试空楼栋列表的处理 ===");
        
        // 测试null
        Map<String, Object> result1 = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(null);
        logger.info("null楼栋列表查询结果: 查询方法={}, 总人数={}", 
                result1.get("queryMethod"), result1.get("totalPersons"));
        
        // 测试空列表
        Map<String, Object> result2 = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(Arrays.asList());
        logger.info("空楼栋列表查询结果: 查询方法={}, 总人数={}", 
                result2.get("queryMethod"), result2.get("totalPersons"));
        
        // 测试包含空字符串的列表
        Map<String, Object> result3 = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(Arrays.asList("", " ", null));
        logger.info("包含空字符串的楼栋列表查询结果: 查询方法={}, 总人数={}", 
                result3.get("queryMethod"), result3.get("totalPersons"));
    }

    /**
     * 测试不存在的楼栋代码
     */
    @Test
    public void testNonExistentBuildingCodes() {
        logger.info("=== 测试不存在的楼栋代码 ===");
        
        List<String> nonExistentCodes = Arrays.asList("NONEXISTENT1", "NONEXISTENT2");
        
        Map<String, Object> result = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(nonExistentCodes);
        
        logger.info("不存在楼栋查询结果: 楼栋={}, 总人数={}, 在寝={}, 外出={}, 无记录={}, 查询方法={}", 
                nonExistentCodes, 
                result.get("totalPersons"), 
                result.get("returnedCount"), 
                result.get("notReturnedCount"), 
                result.get("personsWithoutRecords"), 
                result.get("queryMethod"));
    }

    /**
     * 性能对比测试：优化前后的查询时间对比
     */
    @Test
    public void testPerformanceComparison() {
        logger.info("=== 性能对比测试 ===");

        List<String> buildingCodes = Arrays.asList("A1", "A2");

        // 预热
        cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(buildingCodes);

        // 测试多次查询的平均性能
        long totalDuration = 0;
        int testRounds = 5;

        for (int i = 1; i <= testRounds; i++) {
            long startTime = System.currentTimeMillis();
            Map<String, Object> result = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(buildingCodes);
            long duration = System.currentTimeMillis() - startTime;

            totalDuration += duration;

            logger.info("第{}轮性能测试: 耗时={}ms, 查询方法={}", i, duration, result.get("queryMethod"));

            // 输出详细的性能统计
            if (result.containsKey("performanceStats")) {
                Map<String, Object> perfStats = (Map<String, Object>) result.get("performanceStats");
                logger.info("第{}轮详细性能: Redis耗时={}ms, 命中率={}%, 批次大小={}",
                        i, perfStats.get("redisQueryTime"), perfStats.get("redisHitRate"), perfStats.get("batchSize"));
            }
        }

        double averageDuration = (double) totalDuration / testRounds;
        logger.info("性能测试总结: {}轮测试平均耗时={}ms", testRounds, averageDuration);
    }

    /**
     * 测试不同数据量下的性能表现
     */
    @Test
    public void testPerformanceWithDifferentDataSizes() {
        logger.info("=== 不同数据量性能测试 ===");

        // 测试不同规模的楼栋组合
        List<List<String>> testCases = Arrays.asList(
                Arrays.asList("A1"),                    // 单楼栋
                Arrays.asList("A1", "A2"),              // 双楼栋
                Arrays.asList("A1", "A2", "B1"),        // 三楼栋
                Arrays.asList("A1", "A2", "B1", "B2"),  // 四楼栋
                Arrays.asList("A1", "A2", "B1", "B2", "C1") // 五楼栋
        );

        for (int i = 0; i < testCases.size(); i++) {
            List<String> buildingCodes = testCases.get(i);
            logger.info("测试用例{}: 楼栋数量={}, 楼栋列表={}", i + 1, buildingCodes.size(), buildingCodes);

            // 执行查询
            long startTime = System.currentTimeMillis();
            Map<String, Object> result = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(buildingCodes);
            long duration = System.currentTimeMillis() - startTime;

            // 输出结果
            logger.info("测试用例{}结果: 总人数={}, 在寝={}, 外出={}, 无记录={}, 耗时={}ms",
                    i + 1, result.get("totalPersons"), result.get("returnedCount"),
                    result.get("notReturnedCount"), result.get("personsWithoutRecords"), duration);

            // 输出性能统计
            if (result.containsKey("performanceStats")) {
                Map<String, Object> perfStats = (Map<String, Object>) result.get("performanceStats");
                logger.info("测试用例{}性能: Redis耗时={}ms, 分析耗时={}ms, 命中率={}%, 批次={}, 处理速度={}/秒",
                        i + 1, perfStats.get("redisQueryTime"), perfStats.get("analysisTime"),
                        perfStats.get("redisHitRate"), perfStats.get("totalBatches"),
                        perfStats.get("studentsPerSecond"));
            }

            logger.info("---");
        }
    }

    /**
     * Redis性能压力测试
     */
    @Test
    public void testRedisPerformanceStress() {
        logger.info("=== Redis性能压力测试 ===");

        List<String> buildingCodes = Arrays.asList("A1", "A2", "B1", "B2");

        // 连续执行多次查询，测试Redis性能稳定性
        int stressRounds = 10;
        long totalTime = 0;
        long minTime = Long.MAX_VALUE;
        long maxTime = 0;

        for (int i = 1; i <= stressRounds; i++) {
            long startTime = System.currentTimeMillis();
            Map<String, Object> result = cachedDormitoryStatsService.getDormitoryStatisticsByBuildings(buildingCodes);
            long duration = System.currentTimeMillis() - startTime;

            totalTime += duration;
            minTime = Math.min(minTime, duration);
            maxTime = Math.max(maxTime, duration);

            if (i % 3 == 0) {
                logger.info("压力测试进度: {}/{}, 当前耗时={}ms", i, stressRounds, duration);
            }
        }

        double avgTime = (double) totalTime / stressRounds;
        logger.info("Redis压力测试结果: 总轮数={}, 平均耗时={}ms, 最短={}ms, 最长={}ms",
                stressRounds, avgTime, minTime, maxTime);

        // 性能稳定性分析
        double variance = maxTime - minTime;
        double stabilityRatio = variance / avgTime * 100;
        logger.info("性能稳定性分析: 时间方差={}ms, 稳定性比率={}%", variance, stabilityRatio);

        if (stabilityRatio < 50) {
            logger.info("✅ Redis性能稳定，方差比率 < 50%");
        } else {
            logger.warn("⚠️ Redis性能波动较大，建议检查网络或Redis配置");
        }
    }
}
